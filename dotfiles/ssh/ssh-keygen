#!/bin/bash

KEY_NAME="${1}"
KEY_PASS="${2}"
KEY_FILE="${HOME}/.ssh/id_rsa_${KEY_NAME}"

if [[ -z "${KEY_NAME}" ]] || [[ -z "${KEY_PASS}" ]]
then
	echo "Usage: ${0} <FileName> <Passphrase>"
	exit
fi

ssh-keygen -t rsa -m PEM -b 4096 -N "${KEY_PASS}" -C "${KEY_NAME}" -f "${KEY_FILE}"

#echo "${KEY_PWD}" | ssh-add -K -v "${KEY_FILE}" 

#echo "xxxxxxxxxxxxxxxxxxxx" | ssh-add -K -v "./id_rsa_virajp-2020"

# Display private key

# openssl rsa  -passin pass:xxxxxxxxxxxxxxxxxxxx -in 

#diff <( ssh-keygen -y -e -f "<private-key>" ) <( ssh-keygen -y -e -f "<public-key>" ) && echo "All ok"
#diff <( ssh-keygen -y -e -f "./id_rsa_virajp-2020" ) <( ssh-keygen -y -e -f "./id_rsa_virajp-2020.pub" ) && echo "All ok"
