# Added by <PERSON><PERSON><PERSON><PERSON><PERSON>: 'orb' SSH host for Linux machines
# This only works if it's at the top of ssh_config (before any Host blocks).
# This won't be added again if you remove it.
Include ~/.orbstack/ssh/config

Host **********
  User sshd
  PasswordAuthentication yes
  PreferredAuthentications password
  PubkeyAuthentication no

Host *
  User virajp
  StrictHostKeyChecking no
  UserKnownHostsFile=/dev/null
  CheckHostIP yes
  TCPKeepAlive yes
  AddKeysToAgent yes
  UseKeychain yes
  PasswordAuthentication no
  PreferredAuthentications publickey,password
  Protocol 2
  ServerAliveInterval 30
  ServerAliveCountMax 4
  IdentityAgent "~/Library/Group Containers/2BUA8C4S2C.com.1password/t/agent.sock"

Host apk.virajp.me
  User virajp
  StrictHostKeyChecking no
  UserKnownHostsFile=/dev/null
  CheckHostIP yes
  TCPKeepAlive yes
  AddKeysToAgent yes
  UseKeychain yes
  PasswordAuthentication no
  PreferredAuthentications publickey,password
  Protocol 2
  ServerAliveInterval 30
  ServerAliveCountMax 4
  IdentityAgent "~/Library/Group Containers/2BUA8C4S2C.com.1password/t/agent.sock"
