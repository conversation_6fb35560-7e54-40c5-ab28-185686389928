"$schema" = 'https://starship.rs/config-schema.json'

format = """
[](color_orange)\
$os$time\
[](fg:color_orange bg:color_aqua)\
$git_branch\
$git_status\
[](bg:color_yellow fg:color_aqua)\
$directory\
[](fg:color_yellow bg:color_blue)\
$c\
$rust\
$golang\
$nodejs\
$php\
$java\
$kotlin\
$haskell\
$python\
[](bg:color_aqua fg:color_blue)\
$kubernetes\
[](bg:color_red fg:color_aqua)\
$cmd_duration\
[](fg:color_red)\

$character\
$line_break\
[ ](bold green)\
"""

right_format="""
"""

palette = 'viraj_dark'
continuation_prompt = ' '
command_timeout = 500
add_newline = true

[palettes.viraj_dark]
color_white = '#fbf1c7'
color_grey = '#3c3836'
color_bg3 = '#665c54'
color_blue = '#458588'
color_aqua = '#689d6a'
color_green = '#98971a'
color_orange = '#d65d0e'
color_purple = '#b16286'
color_red = '#cc241d'
color_yellow = '#d79921'

[os]
disabled = false
style = "bg:color_orange fg:color_white"

[os.symbols]
Windows = "󰍲"
Ubuntu = "󰕈"
SUSE = ""
Raspbian = "󰐿"
Mint = "󰣭"
Macos = "󰀵"
Manjaro = ""
Linux = "󰌽"
Gentoo = "󰣨"
Fedora = "󰣛"
Alpine = ""
Amazon = ""
Android = ""
Arch = "󰣇"
Artix = "󰣇"
CentOS = ""
Debian = "󰣚"
Redhat = "󱄛"
RedHatEnterprise = "󱄛"

[username]
show_always = true
style_user = "bg:color_orange fg:color_white"
style_root = "bg:color_orange fg:color_white"
format = '[ $user ]($style)'

[directory]
style = "fg:color_white bg:color_yellow"
format = "[ $path ]($style)"
home_symbol = " 🏠 "
truncate_to_repo = false
truncation_length = 4
truncation_symbol = "… /"
use_os_path_sep = true

[directory.substitutions]
"Documents" = " 󰈙 "
"Downloads" = "  "
"Music" = " 󰝚 "
"Pictures" = "  "
"Developer" = " 󰲋 "
"github.com" = " 󰲋 "

[cmd_duration]
style = "bg:color_red"
min_time = 0
format = '[[ $duration ](fg:color_white bg:color_red)]($style)'

[git_branch]
symbol = ""
style = "bg:color_aqua"
format = '[[ $symbol $branch ](fg:color_white bg:color_aqua)]($style)'

[git_status]
style = "bg:color_aqua"
format = '[[($all_status$ahead_behind )](fg:color_white bg:color_aqua)]($style)'

[nodejs]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[c]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[rust]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[golang]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[php]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[java]
symbol = " "
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[kotlin]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[haskell]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[python]
symbol = ""
style = "bg:color_blue"
format = '[[ $symbol( $version) ](fg:color_white bg:color_blue)]($style)'

[docker_context]
disabled = true
symbol = ""
style = "bg:color_white fg:color_aqua"
format = '[[ $symbol( $context) ](fg:color_white bg:color_aqua)]($style)'

[kubernetes]
disabled = false
symbol = "☸"
style = "bg:color_white fg:color_aqua"
format = '[[ $symbol( $context) ](fg:color_white bg:color_aqua)]($style)'
# namespaces = ["default", "kube-system", "kube-public", "kube-node-lease"]

[gcloud]
disabled = true
symbol = "☁️"
# symbol = "🇬️"
style = "bg:color_red"
# format = '[[ $symbol( $context) ](fg:#83a598 bg:color_bg3)]($style)'
format = '[[ $symbol $project ](fg:color_white bg:color_red)]($style)'

[time]
disabled = false
# time_format = "%a, %e %b, %l:%M%P"
time_format = "%l:%M%P"
use_12hr = true
style = "bg:color_orange"
format = '[[ $time ](fg:color_white bg:color_orange)]($style)'

[line_break]
disabled = false

[character]
disabled = false
success_symbol = '[ ✓ ](bold fg:color_green)'
error_symbol = '[ ✗ ](bold fg:color_red)'
vimcmd_symbol = '[](bold fg:color_green)'
vimcmd_replace_one_symbol = '[](bold fg:color_purple)'
vimcmd_replace_symbol = '[](bold fg:color_purple)'
vimcmd_visual_symbol = '[](bold fg:color_yellow)'
