# 95octane related customisations
export alias cd95 = cd ($env.HOME | path join "Projects/github.com/95octane")

# function dev-95octane
#   # Google Play Store (Firebase Distribution: prod-95octane-app)
#   set FIREBASE_APP_ID ""
#   # export FIREBASE_TOKEN="1//0gzAGhopzv39wCgYIARAAGBASNwF-L9IrwYhhYclMLX7Ec5GfNC7dRrUOx8Ls6vxQ2JhVrAb8MkW4mbOJCKW90_8_4OPJuzK-P94"
#   set GOOGLE_APPLICATION_CREDENTIALS ""
# end

# function prod-95octane
#   # Google Play Store (Firebase Distribution: prod-95octane-app)
#   set FIREBASE_APP_ID "1:************:android:0232ae4cd416b3b25098f1"
#   # export FIREBASE_TOKEN="1//0gzAGhopzv39wCgYIARAAGBASNwF-L9IrwYhhYclMLX7Ec5GfNC7dRrUOx8Ls6vxQ2JhVrAb8MkW4mbOJCKW90_8_4OPJuzK-P94"
#   set GOOGLE_APPLICATION_CREDENTIALS "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/95octane/firebase/prod-95octane-app/service-accounts/firebase-adminsdk.json"
# end
