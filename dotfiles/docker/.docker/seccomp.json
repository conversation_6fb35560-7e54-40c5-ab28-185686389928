{"defaultAction": "SCMP_ACT_ERRNO", "defaultErrnoRet": 1, "archMap": [{"architecture": "SCMP_ARCH_X86_64", "subArchitectures": ["SCMP_ARCH_X86", "SCMP_ARCH_X32"]}, {"architecture": "SCMP_ARCH_AARCH64", "subArchitectures": ["SCMP_ARCH_ARM"]}, {"architecture": "SCMP_ARCH_MIPS64", "subArchitectures": ["SCMP_ARCH_MIPS", "SCMP_ARCH_MIPS64N32"]}, {"architecture": "SCMP_ARCH_MIPS64N32", "subArchitectures": ["SCMP_ARCH_MIPS", "SCMP_ARCH_MIPS64"]}, {"architecture": "SCMP_ARCH_MIPSEL64", "subArchitectures": ["SCMP_ARCH_MIPSEL", "SCMP_ARCH_MIPSEL64N32"]}, {"architecture": "SCMP_ARCH_MIPSEL64N32", "subArchitectures": ["SCMP_ARCH_MIPSEL", "SCMP_ARCH_MIPSEL64"]}, {"architecture": "SCMP_ARCH_S390X", "subArchitectures": ["SCMP_ARCH_S390"]}, {"architecture": "SCMP_ARCH_RISCV64", "subArchitectures": null}], "syscalls": [{"names": ["accept", "accept4", "access", "adjtimex", "alarm", "bind", "brk", "cachestat", "capget", "capset", "chdir", "chmod", "chown", "chown32", "clock_adjtime", "clock_adjtime64", "clock_getres", "clock_getres_time64", "clock_gettime", "clock_gettime64", "clock_nanosleep", "clock_nanosleep_time64", "close", "close_range", "connect", "copy_file_range", "creat", "dup", "dup2", "dup3", "epoll_create", "epoll_create1", "epoll_ctl", "epoll_ctl_old", "epoll_pwait", "epoll_pwait2", "epoll_wait", "epoll_wait_old", "eventfd", "eventfd2", "execve", "execveat", "exit", "exit_group", "faccessat", "faccessat2", "fadvise64", "fadvise64_64", "fallocate", "fanotify_mark", "fchdir", "fchmod", "fchmodat", "fchmodat2", "fchown", "fchown32", "fchownat", "fcntl", "fcntl64", "fdatasync", "fgetxattr", "fl<PERSON><PERSON><PERSON><PERSON>", "flock", "fork", "fremovexattr", "fsetxattr", "fstat", "fstat64", "fstatat64", "fstatfs", "fstatfs64", "fsync", "ftrun<PERSON>", "ftruncate64", "futex", "futex_requeue", "futex_time64", "futex_wait", "futex_waitv", "futex_wake", "futimesat", "getcpu", "getcwd", "getdents", "getdents64", "<PERSON><PERSON><PERSON>", "getegid32", "<PERSON><PERSON><PERSON>", "geteuid32", "getgid", "getgid32", "getgroups", "getgroups32", "getitimer", "getpeername", "getpgid", "getpgrp", "getpid", "<PERSON><PERSON><PERSON>", "getpriority", "getrandom", "get<PERSON><PERSON>d", "getresgid32", "getresuid", "getresuid32", "getrlimit", "get_robust_list", "getrusage", "getsid", "getsockname", "getsockopt", "get_thread_area", "gettid", "gettimeofday", "getuid", "getuid32", "getxa<PERSON><PERSON>", "inotify_add_watch", "inotify_init", "inotify_init1", "inotify_rm_watch", "io_cancel", "ioctl", "io_destroy", "io_getevents", "io_pgetevents", "io_pgetevents_time64", "ioprio_get", "ioprio_set", "io_setup", "io_submit", "ipc", "kill", "landlock_add_rule", "landlock_create_ruleset", "landlock_restrict_self", "lchown", "lchown32", "lgetxattr", "link", "linkat", "listen", "listxattr", "llistxattr", "_llseek", "lremovexattr", "lseek", "lsetxattr", "lstat", "lstat64", "madvise", "map_shadow_stack", "membarrier", "memfd_create", "memfd_secret", "mincore", "mkdir", "mkdirat", "mknod", "mknodat", "mlock", "mlock2", "m<PERSON>all", "mmap", "mmap2", "mprotect", "mq_getsetattr", "mq_notify", "mq_open", "mq_timedreceive", "mq_timedreceive_time64", "mq_timedsend", "mq_timedsend_time64", "mq_unlink", "mremap", "msgctl", "msgget", "msgrcv", "msgsnd", "msync", "munlock", "mun<PERSON><PERSON>", "mun<PERSON>p", "name_to_handle_at", "nanosleep", "newfstatat", "_newselect", "open", "openat", "openat2", "pause", "pidfd_open", "pidfd_send_signal", "pipe", "pipe2", "pkey_alloc", "pkey_free", "pkey_mprotect", "poll", "ppoll", "ppoll_time64", "prctl", "pread64", "preadv", "preadv2", "prlimit64", "process_mrelease", "pselect6", "pselect6_time64", "pwrite64", "pwritev", "pwritev2", "read", "readahead", "readlink", "readlinkat", "readv", "recv", "recvfrom", "recvmmsg", "recvmmsg_time64", "recvmsg", "remap_file_pages", "removexattr", "rename", "renameat", "renameat2", "restart_syscall", "rmdir", "rseq", "rt_sigaction", "rt_sigpending", "rt_sigprocmask", "rt_sigqueueinfo", "rt_sigreturn", "rt_sigsuspend", "rt_sigtimedwait", "rt_sigtimedwait_time64", "rt_tgsigqueueinfo", "sched_getaffinity", "sched_getattr", "sched_getparam", "sched_get_priority_max", "sched_get_priority_min", "sched_getscheduler", "sched_rr_get_interval", "sched_rr_get_interval_time64", "sched_setaffinity", "sched_setattr", "sched_setparam", "sched_setscheduler", "sched_yield", "seccomp", "select", "semctl", "semget", "semop", "semtimedop", "semtimedop_time64", "send", "sendfile", "sendfile64", "sendmmsg", "sendmsg", "sendto", "setfsgid", "setfsgid32", "set<PERSON>uid", "setfsuid32", "<PERSON><PERSON>d", "setgid32", "setgroups", "setgroups32", "setitimer", "setpgid", "setpriority", "set<PERSON>gi<PERSON>", "setregid32", "<PERSON><PERSON><PERSON><PERSON>", "setresgid32", "setresuid", "setresuid32", "set<PERSON><PERSON>", "setreuid32", "setrlimit", "set_robust_list", "setsid", "setsockopt", "set_thread_area", "set_tid_address", "setuid", "setuid32", "<PERSON><PERSON><PERSON><PERSON>", "shmat", "shmctl", "shmdt", "shmget", "shutdown", "sigaltsta<PERSON>", "signalfd", "signalfd4", "sigprocmask", "sigret<PERSON>", "socketcall", "socketpair", "splice", "stat", "stat64", "statfs", "statfs64", "statx", "symlink", "symlinkat", "sync", "sync_file_range", "syncfs", "sysinfo", "tee", "tgkill", "time", "timer_create", "timer_delete", "timer_getoverrun", "timer_gettime", "timer_gettime64", "timer_settime", "timer_settime64", "timerfd_create", "timerfd_gettime", "timerfd_gettime64", "timerfd_settime", "timerfd_settime64", "times", "tkill", "truncate", "truncate64", "ugetrlimit", "umask", "uname", "unlink", "unlinkat", "utime", "utimensat", "utimensat_time64", "utimes", "vfork", "vmsplice", "wait4", "waitid", "wait<PERSON>", "write", "writev"], "action": "SCMP_ACT_ALLOW"}, {"names": ["process_vm_readv", "process_vm_writev", "ptrace"], "action": "SCMP_ACT_ALLOW", "includes": {"minKernel": "4.8"}}, {"names": ["socket"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 40, "op": "SCMP_CMP_NE"}]}, {"names": ["personality"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 0, "op": "SCMP_CMP_EQ"}]}, {"names": ["personality"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 8, "op": "SCMP_CMP_EQ"}]}, {"names": ["personality"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 131072, "op": "SCMP_CMP_EQ"}]}, {"names": ["personality"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 131080, "op": "SCMP_CMP_EQ"}]}, {"names": ["personality"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 4294967295, "op": "SCMP_CMP_EQ"}]}, {"names": ["sync_file_range2", "swapcontext"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["ppc64le"]}}, {"names": ["arm_fadvise64_64", "arm_sync_file_range", "sync_file_range2", "breakpoint", "cacheflush", "set_tls"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["arm", "arm64"]}}, {"names": ["arch_prctl"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["amd64", "x32"]}}, {"names": ["modify_ldt"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["amd64", "x32", "x86"]}}, {"names": ["s390_pci_mmio_read", "s390_pci_mmio_write", "s390_runtime_instr"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["s390", "s390x"]}}, {"names": ["riscv_flush_icache"], "action": "SCMP_ACT_ALLOW", "includes": {"arches": ["riscv64"]}}, {"names": ["open_by_handle_at"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_DAC_READ_SEARCH"]}}, {"names": ["bpf", "clone", "clone3", "fanotify_init", "fsconfig", "fsmount", "fsopen", "fspick", "lookup_dcookie", "mount", "mount_setattr", "move_mount", "open_tree", "perf_event_open", "quotactl", "quotactl_fd", "setdomainname", "sethostname", "setns", "syslog", "umount", "umount2", "unshare"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_ADMIN"]}}, {"names": ["clone"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 0, "value": 2114060288, "op": "SCMP_CMP_MASKED_EQ"}], "excludes": {"caps": ["CAP_SYS_ADMIN"], "arches": ["s390", "s390x"]}}, {"names": ["clone"], "action": "SCMP_ACT_ALLOW", "args": [{"index": 1, "value": 2114060288, "op": "SCMP_CMP_MASKED_EQ"}], "comment": "s390 parameter ordering for clone is different", "includes": {"arches": ["s390", "s390x"]}, "excludes": {"caps": ["CAP_SYS_ADMIN"]}}, {"names": ["clone3"], "action": "SCMP_ACT_ERRNO", "errnoRet": 38, "excludes": {"caps": ["CAP_SYS_ADMIN"]}}, {"names": ["reboot"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_BOOT"]}}, {"names": ["chroot"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_CHROOT"]}}, {"names": ["delete_module", "init_module", "finit_module"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_MODULE"]}}, {"names": ["acct"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_PACCT"]}}, {"names": ["kcmp", "pidfd_getfd", "process_madvise", "process_vm_readv", "process_vm_writev", "ptrace"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_PTRACE"]}}, {"names": ["iopl", "ioperm"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_RAWIO"]}}, {"names": ["settimeofday", "stime", "clock_settime", "clock_settime64"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_TIME"]}}, {"names": ["vhangup"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_TTY_CONFIG"]}}, {"names": ["get_mempolicy", "mbind", "set_mempolicy", "set_mempolicy_home_node"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYS_NICE"]}}, {"names": ["syslog"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_SYSLOG"]}}, {"names": ["bpf"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_BPF"]}}, {"names": ["perf_event_open"], "action": "SCMP_ACT_ALLOW", "includes": {"caps": ["CAP_PERFMON"]}}]}