set shell := ["zsh", "-cu"]
set quiet := true

@_default:
  just --list

[group('dotfiles')]
[doc('Install dotfiles using stow')]
@stow-install:
  echo "Installing up dotfiles ..."
  cd dotfiles && stow --dir=. --target=$HOME --verbose */

[group('dotfiles')]
[doc('Re-install dotfiles using stow')]
@stow-reinstall:
  echo "Re-installing dotfiles ..."
  cd dotfiles && stow --restow --dir=. --target=$HOME --verbose */

[group('dotfiles')]
[doc('Cleanup dotfiles using stow')]
@stow-cleanup:
  echo "Cleaning up dotfiles ..."
  cd dotfiles && stow --delete --dir=. --target=$HOME --verbose */

[group('dotfiles')]
[doc('Simulate dotfiles using stow')]
@stow-simulate:
  echo "Simulate dotfiles ..."
  cd dotfiles && stow --dir=. --target=$HOME --verbose --simulate */

[group('brewfile')]
[doc('Generate brewfile for all installed packages')]
@brew:
  echo "Generating brewfile ..."
  brew bundle dump --all --force --describe --file=./Brewfile
  echo "Printing the diff ... "
  git diff --color=always | diff-so-fancy

[group('brewfile')]
[doc('Remove packages not in brewfile')]
@brew-cleanup:
  echo "Removing packages not in Brewfile ..."
  brew bundle cleanup --force --describe --file=./Brewfile
  echo "Autoremoving packages ..."
  brew autoremove --verbose

[group('brewfile')]
[doc('Generate nushell init scripts for oh-my-posh & zoxide ... ')]
@nu-init:
  echo "Generating init script for oh-my-posh ..."
  oh-my-posh init nu --print --config ~/.config/oh-my-posh.yaml > "./dotfiles/nushell/Library/Application Support/nushell/vendor/autoload/99-oh-my-posh.nu"
  echo "Generating init script for zoxide ..."
  zoxide init nushell > "./dotfiles/nushell/Library/Application Support/nushell/vendor/autoload/99-zoxide.nu"

[group('brewfile')]
[doc('Generate fish init scripts for oh-my-posh, direnv & zoxide ... ')]
@fish-init:
  echo "Generating init script for oh-my-posh ..."
  oh-my-posh init fish --print --config ~/.config/oh-my-posh.yaml > "./dotfiles/fish/.config/fish/conf.d/99-oh-my-posh.fish"
  echo "Generating init script for direnv ..."
  direnv hook fish > "./dotfiles/fish/.config/fish/conf.d/99-direnv.fish"
  echo "Generating init script for zoxide ..."
  zoxide init fish > "./dotfiles/fish/.config/fish/conf.d/99-zoxide.fish"
