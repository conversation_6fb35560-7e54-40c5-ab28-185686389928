tap "derailed/k9s"
tap "homebrew/bundle"
# Build OCI images from APK packages directly without Dockerfile
brew "apko"
# Portable library for network traffic capture
brew "libpcap"
# ARP scanning and fingerprinting tool
brew "arp-scan"
# Bourne-Again <PERSON>ell, a UNIX command interpreter
brew "bash"
# Mozilla CA certificate store
brew "ca-certificates"
# Cryptography and SSL/TLS Toolkit
brew "openssl@3"
# C library of Git core methods that is re-entrant and linkable
brew "libgit2"
# Clone of cat(1) with syntax highlighting and Git integration
brew "bat"
# Resource monitor. C++ version and continuation of bashtop and bpytop
brew "btop"
# Asynchronous DNS library
brew "c-ares"
# Library for manipulating PNG images
brew "libpng"
# Software library to render fonts
brew "freetype"
# Perl compatible regular expressions library with a new API
brew "pcre2"
# Core application library for C
brew "glib"
# X.Org: Interface to the X Window System protocol
brew "libxcb"
# X.Org: Core X11 protocol client library
brew "libx11"
# Low-level library for pixel manipulation
brew "pixman"
# Vector graphics library with cross-device output support
brew "cairo"
# Console Matrix
brew "cmatrix"
# Dependency manager for Cocoa projects
brew "cocoapods"
# GNU File, Shell, and Text utilities
brew "coreutils"
# Container Signing
brew "cosign"
# Tool for interacting with remote images and registries
brew "crane"
# HTTP/2 C Library
brew "libnghttp2"
# Extremely Fast Compression algorithm
brew "lz4"
# General-purpose data compression with high compression ratio
brew "xz"
# Get a file from an HTTP, HTTPS or FTP server
brew "curl"
# Modern diagram scripting language that turns text to diagrams
brew "d2"
# JPEG image codec that aids compression and decompression
brew "jpeg-turbo"
# TIFF library and utilities
brew "libtiff"
# Library for command-line editing
brew "readline"
# Command-line interface for SQLite
brew "sqlite"
# Secure runtime for JavaScript and TypeScript
brew "deno"
# Good-lookin' diffs with diff-highlight and more
brew "diff-so-fancy"
# Diff for Docker and OCI container images
brew "diffoci"
# Text-based UI library
brew "ncurses"
# Load/unload environment variables based on $PWD
brew "direnv"
# Tool for exploring each layer in a docker image
brew "dive"
# Command-line DNS Client for Humans
brew "doggo"
# Modern, maintained replacement for ls
brew "eza"
# User-friendly command-line shell for UNIX-like operating systems
brew "fish"
# Fast and simple Node.js version manager
brew "fnm"
# Command-line fuzzy finder written in Go
brew "fzf"
# Toolkit for image loading and pixel buffer manipulation
brew "gdk-pixbuf"
# GitHub command-line tool
brew "gh"
# Library and utilities for processing GIFs
brew "giflib"
# Distributed revision control system
brew "git"
# Audit git repos for secrets
brew "gitleaks"
# Open source programming language to build simple/reliable/efficient software
brew "go"
# Task is a task runner/build tool that aims to be simpler and easier to use
brew "go-task"
# Ping, but with a graph
brew "gping"
# Vulnerability scanner for container images and filesystems
brew "grype"
# OpenType text shaping engine
brew "harfbuzz"
# Kubernetes package manager
brew "helm"
# Interpreted, interactive, object-oriented programming language
brew "python@3.13", link: false
# User-friendly cURL replacement (command-line HTTP client)
brew "httpie"
# Ping-like tool for HTTP requests
brew "httping"
# Docker Hub experimental CLI tool
brew "hub-tool"
# C/C++ and Java libraries for Unicode and globalization
brew "icu4c@76"
# Calculate various network masks, etc. from a given IP address
brew "ipcalc"
# Lightweight and flexible command-line JSON processor
brew "jq"
# Handy way to save and run project-specific commands
brew "just"
# Lazier way to manage everything docker
brew "lazydocker"
# Simple terminal UI for git commands
brew "lazygit"
# Common error values for all GnuPG components
brew "libgpg-error"
# X.509 and CMS library
brew "libksba"
# General purpose TCP-IP emulator
brew "libslirp"
# Multi-platform support library with a focus on asynchronous I/O
brew "libuv"
# Mac App Store command-line interface
brew "mas"
# Build APKs from source code
brew "melange"
# Simple tool to make locally trusted development certificates
brew "mkcert"
# Low-level cryptographic library
brew "nettle"
# Modern shell for the GitHub era
brew "nushell"
# Core utilities for Python packages
brew "python-packaging"
# Development kit for the Java programming language
brew "openjdk"
# Library to load and enumerate PKCS#11 modules
brew "p11-kit"
# Passphrase entry dialog utilizing the Assuan protocol
brew "pinentry"
# Pinentry for GPG on Mac
brew "pinentry-mac"
# Execute binaries from Python packages in isolated environments
brew "pipx"
# Package compiler and linker metadata toolkit
brew "pkgconf"
# Wrapper to colorize and simplify ping's output
brew "prettyping"
# Show ps output as a tree
brew "pstree"
# Interpreted, interactive, object-oriented programming language
brew "python@3.12"
# Easy and Repeatable Kubernetes Development
brew "skaffold"
# Cross-shell prompt for astronauts
brew "starship"
# Organize software neatly under a single directory tree (e.g. /usr/local)
brew "stow"
# CLI for generating a Software Bill of Materials from container images
brew "syft"
# Secrets management tool for developers
brew "teller"
# Tool to build, change, and version infrastructure
brew "terraform"
# CLI tool to generate terraform files from existing infrastructure
brew "terraformer"
# Static analysis security scanner for your terraform code
brew "tfsec"
# Display directories as trees (with optional color/HTML output)
brew "tree"
# Validating, recursive, caching DNS resolver
brew "unbound"
# Extremely fast Python package installer and resolver, written in Rust
brew "uv"
# Executes a program periodically, showing output fullscreen
brew "watch"
# Internet file retriever
brew "wget"
# Process YAML, JSON, XML, CSV and properties documents from the CLI
brew "yq"
# Shell extension to navigate your filesystem faster
brew "zoxide"
# UNIX shell (command interpreter)
brew "zsh"
# Kubernetes CLI To Manage Your Clusters In Style!
brew "derailed/k9s/k9s"
# Password manager that keeps all passwords secure behind one password
cask "1password", args: { appdir: "/Applications" }
# Application launcher and productivity software
cask "alfred"
# Command-line tools for building and debugging Android apps
cask "android-commandlinetools"
# Tools for building Android applications
cask "android-studio"
# Web browser focusing on privacy
cask "brave-browser"
# Anthropic's official Claude AI desktop app
cask "claude"
# Write, edit, and chat about your code with AI
cask "cursor"
# Voice and text chat software
cask "discord"
# Find files, folders, or contents in any file
cask "easyfind"
# UI toolkit for building applications for mobile, web and desktop
cask "flutter"
# Download manager with a torrent client
cask "folx"
cask "font-fira-code-nerd-font"
cask "font-hack-nerd-font"
cask "font-iosevka-nerd-font"
cask "font-iosevka-term-nerd-font"
# Set of tools to manage resources and applications hosted on Google Cloud
cask "google-cloud-sdk"
# Open-source video transcoder
cask "handbrake"
# Terminal emulator as alternative to Apple's Terminal app
cask "iterm2"
# End-to-end encryption software
cask "keybase"
# App to write, plan, collaborate, and get organised
cask "notion"
# Replacement for Docker Desktop
cask "orbstack"
# Collection of apps available by subscription
cask "setapp"
# Team communication and collaboration software
cask "slack"
# Graphical client for Git version control
cask "sourcetree"
# Music streaming service
cask "spotify"
# Mux and tag mp4 files
cask "subler"
# Text editor for code, markup and prose
cask "sublime-text"
# Tool to explore all the running tasks (processes)
cask "taskexplorer"
# Disposable email client
cask "tempbox"
# Open-source code editor
cask "visual-studio-code"
# Multimedia player
cask "vlc"
# Rust-based terminal
cask "warp"
# Native desktop client for WhatsApp
cask "whatsapp"
# Video communication and virtual meeting platform
cask "zoom", args: { appdir: "~/Applications" }
mas "1Password for Safari", id: **********
mas "Affinity Designer 2", id: **********
mas "Affinity Photo 2", id: **********
mas "Battery Health 2", id: **********
mas "Cardhop", id: **********
mas "Compressor", id: 424390742
mas "DaisyDisk", id: 411643860
mas "Developer", id: 640199958
mas "Disk Doctor", id: 455970963
mas "Disk Map", id: 715464874
mas "Duplicate Detective", id: 686428787
mas "Final Cut Pro", id: 424389933
mas "HP Smart", id: **********
mas "Keynote", id: 409183694
mas "Magnet", id: 441258766
mas "Microsoft Excel", id: 462058435
mas "Microsoft PowerPoint", id: 462062816
mas "Microsoft Word", id: 462054704
mas "MindNode", id: **********
mas "Numbers", id: 409203825
mas "Okta Verify", id: 490179405
mas "Pages", id: 409201541
mas "PDF Protector", id: 566631581
mas "PDF Squeezer", id: **********
mas "Telegram", id: 747648890
mas "TestFlight", id: 899247664
mas "The Unarchiver", id: 425424353
mas "Transporter", id: **********
mas "UTM", id: **********
mas "VN", id: **********
mas "Xcode", id: 497799835
vscode "bierner.markdown-mermaid"
vscode "bmalehorn.vscode-fish"
vscode "davidanson.vscode-markdownlint"
vscode "donjayamanne.githistory"
vscode "foxundermoon.shell-format"
vscode "gruntfuggly.todo-tree"
vscode "ms-azuretools.vscode-containers"
vscode "ms-python.debugpy"
vscode "ms-python.python"
vscode "ms-python.vscode-pylance"
vscode "oderwat.indent-rainbow"
vscode "pkief.material-icon-theme"
vscode "redhat.vscode-yaml"
vscode "skellock.just"
vscode "usernamehw.errorlens"
