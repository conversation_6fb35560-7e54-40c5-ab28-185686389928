# macOS Setup

Follow these steps to setup macOS.

## Steps

- [Create account](./docs/account.md)
- [Configure Shell](./docs/shell.md)
- [Install Homebrew](./docs/homebrew.md)
- [Install fonts](./docs/fonts.md)
- [ZSH Shell](./docs/zsh.md)
- [Setup Hostname & Diskname](./docs/host.md)
- [Install Rosetta](./docs/rosetta.md)
- [macOS: Settings](./docs/settings.md)
- [Setup TouchID for sudo](./docs/touchid-sudo.md)
- [Install tools](./docs/tools.md)
- [Setup GPG](./docs/gpg.md)

## Final steps: Update tools & macOS

```bash
# Update everything
updateall

# Update macOS
sudo softwareupdate --install --all --restart --verbose
```

## Install these tools manually

- [Brave Browser](https://brave.com/)
- [Cloudflare Wrap](https://*******/)
- [Docker for Desktop](https://www.docker.com/products/docker-desktop)
- [SnapDownloader](https://snapdownloader.com/downloads)
- [Spatial Media Metadata Injector](https://github.com/google/spatial-media/releases)
- [Wondershare UniConverter 14](https://videoconverter.wondershare.com/buy-uniconverter-14-with-discount.html)
- [Zoom (user account)](https://zoom.us/support/download)
- [Adobe Creative Cloud](https://creativecloud.adobe.com/)
- [Insta360 Studio 2023](https://www.insta360.com/download/insta360-oners)

## Brew commands

> To update the `Brewfile` with latest installed packages

```bash
brew bundle dump --all --force --describe --file=./Brewfile
```
